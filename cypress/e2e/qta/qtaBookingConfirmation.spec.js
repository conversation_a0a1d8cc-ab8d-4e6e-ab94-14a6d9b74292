import { HOTELS_PATH, POINTS_EARN_ENABLED, QTA_SCREENSIZES } from 'config';
import { fillAdyenPaymentForm, submitPaymentOptionsForm, submitPersonalDetailsForm, submitQantasPointsForm } from '../../support/common';
import { testId } from '../../support/selectors';

describe('QTA Header', () => {
  QTA_SCREENSIZES.forEach((screenSize) => {
    describe('Menu link', () => {
      it(`mobile header menu button should not be present for ${screenSize}`, () => {
        cy.viewport(screenSize);
        cy.visitQtaSearchWithStayAttributes();
        cy.findByTestId('menu-button').should('not.exist');
      });
    });
  });
});

describe('QTA booking confirmation layout', { testIsolation: false }, () => {
  before(() => {
    cy.intercept('POST', `${HOTELS_PATH}/api/ui/quotes`);
    cy.visitQtaCheckoutWithStayAttributes();
    submitPersonalDetailsForm();
    if (POINTS_EARN_ENABLED) {
      submitQantasPointsForm();
    }
    submitPaymentOptionsForm();
    fillAdyenPaymentForm();

    cy.findByTestId('confirm-and-pay-form').within(() => {
      cy.findByLabelText(/I agree/i)
        .type('I agree', { force: true })
        .check();
      cy.findByTestId('payment-button').click({ force: true });
    });
  });
});

describe(`QTA Campaign Messaging for iphone-x`, { testIsolation: false }, () => {
  before(() => {
    cy.visitQtaSearchWithStayAttributes();
    cy.viewport('iphone-x');
  });

  it('should render correctly', () => {
    cy.get(testId('campaign-banner-wrapper')).should('exist');
  });
});
