import { testId, clickTimes } from '../helpers';
import { HOTELS_PATH } from 'config/env';
import { QUERY_DATE_FORMAT } from '../../support/commonParams';
import { format, addDays, startOfDay } from 'date-fns';
import { PRIVACY_AND_SECURITY_URL, TERMS_OF_USE_URL } from 'config/brand';

const LOCATION_INPUT = 'location-search-input';
const LOCATION_RESULT = 'location-search-result';
const checkInDate = startOfDay(addDays(Date.now(), 10));
const checkOutDate = startOfDay(addDays(Date.now(), 20));
const OCCUPANT_PICKER_INPUT = testId('occupant-picker-input');
const CHILDREN_INCREMENT = `${testId('occupant-picker-children')} ${testId('increment')}`;

Cypress.Commands.add('visitHomepage', () => {
  cy.visit({ url: HOTELS_PATH, retryOnStatusCodeFailure: true, retryOnNetworkFailure: true });
});

Cypress.Commands.add('searchForLocation', (searchText, locationResultsCount, location) => {
  const locationKey = `search-locations-${searchText}`;
  const propertiesKey = `search-properties-${searchText}`;

  cy.intercept(`${HOTELS_PATH}/api/ui/locations?name=${searchText}`).as(locationKey);
  cy.intercept(`${HOTELS_PATH}/api/ui/properties?name=${searchText}`).as(propertiesKey);

  cy.findAllByTestId(LOCATION_INPUT)
    .first()
    .click()
    .should('have.value', '')
    .type(searchText)
    .wait([`@${locationKey}`, `@${propertiesKey}`]);

  cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', locationResultsCount).contains(location).click();
});

Cypress.Commands.add('searchForAirbnbLocation', (searchText, locationResultsCount, location) => {
  const locationKey = `search-locations-${searchText}`;

  cy.intercept(`${HOTELS_PATH}/api/ui/locations?name=${searchText}`).as(locationKey);

  cy.findAllByTestId(LOCATION_INPUT)
    .last()
    .click()
    .should('have.value', '')
    .type(searchText)
    .wait([`@${locationKey}`]);

  cy.findAllByTestId(LOCATION_RESULT).should('have.length.greaterThan', locationResultsCount).contains(location).click();
});

Cypress.Commands.add('addChildren', (panel = 'hotels') => {
  const occupantPickerInput =
    panel === 'hotels'
      ? cy.get(`${testId('hotels-panel')} ${OCCUPANT_PICKER_INPUT}`).last()
      : cy.get(`${testId('airbnb-panel')} ${OCCUPANT_PICKER_INPUT}`).last();
  occupantPickerInput.click();
  clickTimes(cy.get(CHILDREN_INCREMENT), 3);
  cy.get(testId('done-button')).click();
});

Cypress.Commands.add('assertLocationUpdated', (searchText) => {
  cy.findByTestId(LOCATION_INPUT).should(($el) => expect($el.attr('placeholder')).to.match(new RegExp(searchText, 'i')));
  cy.get('title').should(($el) => expect($el.text()).to.match(new RegExp(searchText, 'i')));
});

Cypress.Commands.add('assertCommonRouteToQueryParams', () => {
  cy.assertRoutedToQueryParam('checkIn', format(checkInDate, QUERY_DATE_FORMAT));
  cy.assertRoutedToQueryParam('checkOut', format(checkOutDate, QUERY_DATE_FORMAT));

  cy.assertRoutedToQueryParam('adults', '2');
  cy.assertRoutedToQueryParam('children', '3');
});

Cypress.Commands.add('assertNavigationMenuItems', () => {
  cy.findAllByTestId('hotels-link')
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/search\/list/);

  cy.findAllByTestId('destinations-desktop-link').first().should('have.attr', 'href');

  cy.findAllByTestId('deals-link')
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/deals/);

  cy.findAllByTestId('luxe-link')
    .first()
    .should('have.attr', 'href')
    .and('match', /holidays\/deals/);
});

Cypress.Commands.add('assertPhoneNavigationMenuItems', () => {
  cy.get(`${testId('phone-menus')} ${testId('manage-bookings-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/manage\/bookings/);

  cy.get(`${testId('phone-menus')} ${testId('hotels-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/search\/list/);

  cy.get(`${testId('phone-menus')} ${testId('destinations-mobile-link')}`)
    .first()
    .should('have.attr', 'href');

  cy.get(`${testId('phone-menus')} ${testId('deals-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/deals/);

  cy.get(`${testId('phone-menus')} ${testId('luxe-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /holidays\/deals/);

  cy.get(`${testId('phone-menus')} ${testId('phone-contact-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/contact-us/);

  cy.get(`${testId('phone-menus')} ${testId('phone-privacy-security-link')}`)
    .first()
    .should('have.attr', 'href', PRIVACY_AND_SECURITY_URL);

  cy.get(`${testId('phone-menus')} ${testId('phone-term-of-use-link')}`)
    .first()
    .should('have.attr', 'href', TERMS_OF_USE_URL);

  cy.get(`${testId('phone-menus')} ${testId('phone-faqs-link')}`)
    .first()
    .should('have.attr', 'href')
    .and('match', /hotels\/faqs/);
});

Cypress.Commands.add('checkFeaturedCampaignsCarousel', () => {
  cy.findAllByTestId('carousel-item').should('have.length.at.least', 1);
  cy.findAllByTestId('carousel-item').should('have.length.at.most', 5);
  cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'false');
  cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'true');
  cy.findAllByTestId('carousel-item').eq(2).should('have.attr', 'aria-hidden', 'true');

  cy.findByTestId('next-carousel-btn').click();
  cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'true');
  cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'false');
  cy.findAllByTestId('carousel-item').eq(2).should('have.attr', 'aria-hidden', 'true');

  cy.findByTestId('prev-carousel-btn').click();
  cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'false');
  cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'true');
  cy.findAllByTestId('carousel-item').eq(2).should('have.attr', 'aria-hidden', 'true');

  cy.findAllByTestId('breadcrumb-carousel-btn').eq(2).click();
  cy.findAllByTestId('carousel-item').eq(0).should('have.attr', 'aria-hidden', 'true');
  cy.findAllByTestId('carousel-item').eq(1).should('have.attr', 'aria-hidden', 'true');
  cy.findAllByTestId('carousel-item').eq(2).should('have.attr', 'aria-hidden', 'false');
});
