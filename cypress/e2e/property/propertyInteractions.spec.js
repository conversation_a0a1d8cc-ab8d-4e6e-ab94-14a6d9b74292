import { addDays, startOfDay } from 'date-fns';
import { testId } from '../../support/helpers';
import {
  CHECKED_PAY_WITH_INPUT,
  PAY_WITH_LABEL,
  USE_POINTS,
  USE_CASH,
  USE_POINTS_PLUS_PAY,
  defaultPayWith,
} from '../../support/commonParams';

describe('Points+Pay', { testIsolation: false }, () => {
  const POINTS_PAY_WRAPPER = testId('points-pay-wrapper');
  const POINTS_AMOUNT_WRAPPER = testId('points-input');
  const CASH_AMOUNT_WRAPPER = testId('cash-input');
  const SELECT_OFFER_BUTTON = testId('offer-checkout-link');

  beforeEach(() => {
    cy.visitClassicPropertyWithStayAttributes();
  });

  describe('click on points+pay checkbox', () => {
    beforeEach(() => {
      cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS_PLUS_PAY);
    });

    it('Use points+Pay should be selected and slider visible', () => {
      cy.get(PAY_WITH_LABEL).should('contain.text', USE_POINTS_PLUS_PAY);
      cy.get(POINTS_PAY_WRAPPER).should('exist');
    });

    describe('points+pay slider', () => {
      describe('points amount', () => {
        it('default points value should be greater than 5000', () => {
          cy.get(POINTS_AMOUNT_WRAPPER)
            .should('exist')
            .invoke('val')
            .then((text) => {
              const cleanText = text.replace(/,/g, '');
              const pointsValue = parseInt(cleanText);
              expect(pointsValue).to.be.greaterThan(5000);
            });
        });
      });

      describe('select button', () => {
        it('should include the correct initialCash amount', () => {
          cy.getInitialCashAmountFromSelectButton(SELECT_OFFER_BUTTON).then((initialCashAmount) => {
            cy.get(CASH_AMOUNT_WRAPPER).first().should('have.value', initialCashAmount);
          });
        });
      });

      it('to the right, it increases cash and decreases points', () => {
        cy.testPointsSliderMovement('right', 'increase', 'decrease');
      });

      it('to the left, it decreases cash and increases points', () => {
        cy.testPointsSliderMovement('left', 'decrease', 'increase');
      });
    });
  });
});

describe('Pay with', () => {
  const OFFER_CARD = testId('offer-card-expanded');
  const CASH_SYMBOL = testId('cash-currency');
  const POINTS_SYMBOL = testId('luxe-currency');
  const CURRENCY_SYMBOL = testId('currency-symbol-not-from-price');

  describe('default value', () => {
    before(() => {
      cy.visitPropertyWithStayAttributes();
    });

    it(`defaults to ${defaultPayWith}`, () => {
      cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', defaultPayWith);
    });
  });

  describe('selecting pay with cash', { testIsolation: false }, () => {
    before(() => {
      cy.setupWaitPropertyPage();
      cy.visitPropertyWithStayAttributes();
      cy.waitPropertyPage();
      cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);
      cy.waitPropertyPage();
      cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_CASH);
    });

    it('updates search params', () => {
      cy.assertRoutedToQueryParam('payWith', 'cash');
    });

    it('shows cash as active in pay with toggle', () => {
      cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'cash');
    });

    it('shows cash symbol', () => {
      cy.get(OFFER_CARD)
        .first()
        .within(() => {
          cy.get(CURRENCY_SYMBOL).should('have.text', '$');
        });
    });
  });

  describe('selecting pay with points', { testIsolation: false }, () => {
    before(() => {
      cy.visitPropertyWithStayAttributes();

      cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);
    });

    it('updates search params', () => {
      cy.assertRoutedToQueryParam('payWith', 'points');
    });

    it('show points as active in pay with toggle', () => {
      cy.get(CHECKED_PAY_WITH_INPUT).should('have.value', 'points');
    });

    it('hides cash symbol in search results', () => {
      cy.get(OFFER_CARD)
        .first()
        .within(() => {
          cy.get(CASH_SYMBOL).should('not.exist');
        });
    });

    it('shows points symbol in search results', () => {
      cy.get(OFFER_CARD)
        .first()
        .within(() => {
          cy.get(POINTS_SYMBOL).should('exist');
        });
    });
  });
});

describe('Date picker', { testIsolation: false }, () => {
  before(() => cy.visitPropertyWithStayAttributes());

  it('updates search using mouse', () => {
    const checkInDate = startOfDay(addDays(Date.now(), 5));
    const checkOutDate = startOfDay(addDays(Date.now(), 10));

    cy.setupWaitPropertyPage();

    cy.selectPropertyDatesWithMouse(checkInDate, checkOutDate);
    cy.waitPropertyPage();
    cy.assertSelectedDates(checkInDate, checkOutDate);
  });

  describe('when navigating back', () => {
    it('updates dates', () => {
      const checkInDate1 = startOfDay(addDays(Date.now(), 10));
      const checkOutDate1 = startOfDay(addDays(Date.now(), 20));
      const checkInDate2 = startOfDay(addDays(Date.now(), 1));
      const checkOutDate2 = startOfDay(addDays(Date.now(), 3));

      cy.setupWaitPropertyPage();

      cy.selectPropertyDatesWithMouse(checkInDate1, checkOutDate1);
      cy.waitPropertyPage();
      cy.assertSelectedDates(checkInDate1, checkOutDate1);

      cy.selectPropertyDatesWithMouse(checkInDate2, checkOutDate2);
      cy.waitPropertyPage();
      cy.assertSelectedDates(checkInDate2, checkOutDate2);

      cy.go('back');
      cy.waitPropertyPage();
      cy.assertSelectedDates(checkInDate1, checkOutDate1);
    });
  });
});

describe('Occupant picker', () => {
  const OCCUPANT_PICKER_INPUT = testId('occupant-picker-input');
  const DONE = testId('done-button');

  beforeEach(() => {
    cy.visitPropertyWithStayAttributes();
    cy.get(OCCUPANT_PICKER_INPUT).last().click();
  });

  it('shows adults, children & infants controls', () => {
    cy.get(testId('occupant-count')).should('be.visible').should('have.length', 3);
  });

  it('updates current query', () => {
    cy.updateOccupantCount('adults', 3, 2, 5);
    cy.updateOccupantCount('adults', -1, 5, 4);

    cy.updateOccupantCount('children', 3, 0, 3);
    cy.updateOccupantCount('children', -1, 3, 2);

    cy.updateOccupantCount('infants', 3, 0, 3);
    cy.updateOccupantCount('infants', -1, 3, 2);

    cy.get(DONE).click();

    cy.assertRoutedToQueryParam('adults', '4');
    cy.assertRoutedToQueryParam('children', '2');
    cy.assertRoutedToQueryParam('infants', '2');
  });

  describe('back navigation', () => {
    it('should update input', () => {
      cy.updateOccupantCount('adults', 3, 2, 5);
      cy.get(DONE).click();
      cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '5 Guests');
      cy.assertRoutedToQueryParam('adults', '5');
      cy.go('back');
      cy.get(OCCUPANT_PICKER_INPUT).should('have.value', '2 Guests');
    });
  });

  describe('Booking for less than 4 guests', () => {
    it('Prompt message should not be visible', () => {
      cy.updateOccupantCount('adults', 1, 2, 3);
      cy.findByTestId('multi-room-prompt-message').should('not.exist');
    });
  });

  describe('Booking for more than 3 guests', () => {
    it('Prompt message should be visible', () => {
      cy.updateOccupantCount('adults', 2, 2, 4);
      cy.findByTestId('multi-room-prompt-message').should('not.be.empty');
      cy.findByTestId('request-callback-number').should('not.be.empty');
      cy.findByTestId('request-callback-cta').should('not.be.empty');
    });
  });
});

describe('Circle-Rating Tooltips', () => {
  beforeEach(() => {
    cy.visitPropertyWithoutStayAttributes('6542');
  });

  it('should click on Circle-Rating and assert visibility of the tooltip on desktop', () => {
    cy.checkTooltipVisibility({
      ratingType: 'rating-circle',
      tooltipRegex: /This is a self assigned rating by the service provider or obtained through Expedia\./,
    });
  });
  it('should click on Circle-Rating element and assert visibility of the tooltip on mobile', () => {
    cy.checkTooltipVisibility({
      ratingType: 'rating-circle',
      tooltipRegex: /This is a self assigned rating by the service provider or obtained through Expedia\./,
      isMobile: true,
    });
  });
});

describe('Star Rating Tooltips', () => {
  beforeEach(() => {
    cy.visitPropertyWithoutStayAttributes('8421');
  });

  it('should hover over Star-Rating and assert visibility of the tooltip on desktop', () => {
    cy.checkTooltipVisibility({
      ratingType: 'rating-star',
      tooltipRegex: /This official star rating is provided by the property, verified by an independent third party\./,
    });
  });
  it('should hover over Star-Rating element and assert visibility of the tooltip on mobile', () => {
    cy.checkTooltipVisibility({
      ratingType: 'rating-star',
      tooltipRegex: /This official star rating is provided by the property, verified by an independent third party\./,
      isMobile: true,
    });
  });
});
