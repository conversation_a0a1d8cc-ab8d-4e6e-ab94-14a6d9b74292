import { HOTELS_PATH } from 'config/env';
import { CHECKOUT_PAGE_RX, PROPERTY_PAGE_RX, testId } from '../../support/helpers';
import { parse as parseQueryString } from 'query-string';

const BACK_LINK = testId('back-link');
const EXPECTED_STAY_ATTRIBUTES = ['checkIn', 'checkOut', 'adults', 'children', 'infants'];
const PROPERTY_CARD = '[data-testid="search-result"] a';
const SELECT_OFFER_BUTTON = testId('offer-checkout-link');
const CHECKOUT_BACK_BUTTON = testId('not-phone-label');

describe('Back to search results links on the property page', () => {
  describe('with valid stay parameters', () => {
    let backToSearchLocation;
    let backLinkQueryString;

    before(() => {
      cy.visitPropertyWithStayAttributes();
      cy.getBackLinkData(BACK_LINK).then(({ backToSearchLocation: location, backLinkQueryString: queryString }) => {
        backToSearchLocation = location;
        backLinkQueryString = queryString;
      });
    });

    it('back to search link is correct and has all expected stay parameters', () => {
      expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/list`);
      expect(backLinkQueryString).to.have.property('location');
      cy.checkStayAttributes(backLinkQueryString, EXPECTED_STAY_ATTRIBUTES);
    });
  });

  describe('without stay parameters', () => {
    let backToSearchLocation;
    let backLinkQueryString;

    before(() => {
      cy.visitPropertyWithoutStayAttributes();
      cy.getBackLinkData(BACK_LINK).then(({ backToSearchLocation: location, backLinkQueryString: queryString }) => {
        backToSearchLocation = location;
        backLinkQueryString = queryString;
      });
    });

    it('back link defaults to search list page with location attribute', () => {
      expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}/search/list`);
      expect(backLinkQueryString).to.have.property('location');
    });
  });

  const searchModes = [
    {
      name: 'Coming from the list search page',
      path: '/search/list',
      setup: () => {
        cy.visitEmptySearch();
        cy.get(PROPERTY_CARD).first().invoke('removeAttr', 'target').click();
      },
    },
    {
      name: 'Coming from the maps search page',
      path: '/search/map',
      setup: () => {
        cy.setupWaitPropertyPage();
        cy.visitMapsSearch();
        cy.get(testId('inline-card')).eq(1).invoke('removeAttr', 'target').click();
        cy.waitPropertyPage();
      },
    },
  ];

  searchModes.forEach(({ name, path, setup }) => {
    describe(name, () => {
      let backToSearchLocation;
      let backLinkQueryString;
      let propertyQueryString;

      before(() => {
        setup();
        cy.location().then((loc) => (propertyQueryString = parseQueryString(loc.search)));
        cy.getBackLinkData(BACK_LINK).then(({ backToSearchLocation: location, backLinkQueryString: queryString }) => {
          backToSearchLocation = location;
          backLinkQueryString = queryString;
        });
      });

      it('back to search link is correct, has all expected stay parameters and these match the property query', () => {
        expect(backToSearchLocation.pathname).to.eq(`${HOTELS_PATH}${path}`);
        expect(backLinkQueryString).to.have.property('location');
        cy.checkStayAttributes(backLinkQueryString, EXPECTED_STAY_ATTRIBUTES, propertyQueryString);
      });
    });
  });
});

describe('Requesting an unknown property id', () => {
  it('returns a 404 status', async () => {
    const response = await cy.request({ url: `${HOTELS_PATH}/properties/9999999999999999`, failOnStatusCode: false });
    cy.wait(2000);
    expect(response.status).to.eq(404);
  });
});

describe('Opening the map on the property page', { testIsolation: false }, () => {
  const MAP_IMAGE = testId('show-map-button');
  const MAP_CONTAINER = testId('google-map-container');
  const MAP_HEADER = testId('modal-header');
  const MAP_CLOSE_BUTTON = testId('close');

  before(() => {
    cy.visitPropertyWithStayAttributes();
    cy.get(MAP_IMAGE, { timeout: 50000 }).click();
  });

  describe('when clicking on the maps image', () => {
    it('shows the map', () => {
      cy.get(MAP_CONTAINER).should('be.visible');
    });
  });

  describe('when clicking on the close button', () => {
    it('hides the map', () => {
      cy.get(`${MAP_HEADER} ${MAP_CLOSE_BUTTON}`).click();
      cy.get(MAP_CONTAINER).should('not.exist');
    });
  });
});

describe('Checkout navigation', { testIsolation: false }, () => {
  const modes = [
    { name: 'cash', isPointsMode: false, hasInitialCashCheck: true },
    { name: 'points', isPointsMode: true, hasInitialCashCheck: false },
  ];

  modes.forEach(({ name, isPointsMode, hasInitialCashCheck }) => {
    describe(`Property to checkout in ${name} mode`, () => {
      before(function () {
        cy.goToCheckoutPage(isPointsMode);
      });

      it('navigates to checkout page with correct stay attributes', function () {
        const propertyQueryString = parseQueryString(this.propertyQueryString);
        const checkoutQueryString = parseQueryString(this.checkoutQueryString);

        expect(this.checkoutLoc.pathname).match(CHECKOUT_PAGE_RX);
        cy.checkStayAttributes(checkoutQueryString, EXPECTED_STAY_ATTRIBUTES, propertyQueryString);

        if (hasInitialCashCheck) {
          expect(checkoutQueryString).not.to.have.property('initialCash');
        }
      });

      it('navigates back to property page', function () {
        const propertyQueryString = parseQueryString(this.propertyQueryString);

        cy.get(CHECKOUT_BACK_BUTTON).first().should('exist').click();
        cy.get(SELECT_OFFER_BUTTON, { timeout: 30000 }).should('be.visible');

        cy.location().then((loc) => {
          expect(parseQueryString(loc.search)).to.deep.eq(propertyQueryString);
          expect(loc.pathname).match(PROPERTY_PAGE_RX);
        });
      });
    });
  });
});
