import { parse as parseQueryString } from 'query-string';
import { testId, clickTimes } from '../helpers';
import { format, differenceInCalendarMonths, addDays, startOfDay } from 'date-fns';

const DATES_INPUT = testId('select-date-input');
const DATE_RANGE_PICKER = `${testId('stay-date-picker')} ${testId('date-range-picker')}`;
const GO_BACK_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-back-1-month')}`;
const GO_FORWARD_1_MONTH = `${DATE_RANGE_PICKER} ${testId('go-forward-1-month')}`;
const CALENDAR_MONTH = `${DATE_RANGE_PICKER} ${testId('calendar-month')}`;
const checkInDate = startOfDay(addDays(Date.now(), 10));
const checkOutDate = startOfDay(addDays(Date.now(), 20));
const DONE_BUTTON = `${testId('stay-date-picker')} ${testId('done-button')}`;

Cypress.Commands.add('assertQueryParam', (assertFn) => {
  cy.location('search').should((search) => {
    assertFn(parseQueryString(search));
  });
});

Cypress.Commands.add('assertRoutedToQueryParam', (queryKey, queryValue) => {
  cy.location().should((location) => {
    const query = parseQueryString(location.search);
    expect(query).to.have.property(queryKey, queryValue);
  });
});

Cypress.Commands.add('scrollIntoViewAndClick', (container, content) => {
  const element = content ? cy.get(container).contains(content) : cy.get(container);
  cy.wait(1000);
  element.scrollIntoView();

  cy.wait(2000);
  element.click({ force: true });
});

Cypress.Commands.add('setPointsChangeCookie', () => {
  cy.session('Set points change cookie', () => {
    cy.setCookie('points_change_notice', 'true');
  });
});

const clickButtonsAndCheck = (index, ratingType) => {
  cy.get('[data-testid="google-maps-property-marker"]').then(($buttons) => {
    if (index < $buttons.length) {
      cy.wrap($buttons[index]).click({ force: true });

      cy.get('body').then(($body) => {
        if ($body.find(`[id="${ratingType}"]:visible`).length > 0) {
          return;
        } else {
          clickButtonsAndCheck(index + 1, ratingType);
        }
      });
    }
  });
};

Cypress.Commands.add('checkTooltipVisibility', ({ ratingType, tooltipRegex, isMobile = false, isMapsView = false, container = null }) => {
  const getContainer = (selector) =>
    container ? cy.get(container, { timeout: 10000 }).find(selector) : cy.get(selector, { timeout: 10000 });

  if (isMobile) {
    cy.viewport(393, 852);

    if (isMapsView) {
      clickButtonsAndCheck(0, ratingType);
    }

    getContainer(`[id="${ratingType}"]:visible`)
      .first()
      .click()
      .click()
      .siblings()
      .first()
      .find('[role="tooltip"]:visible')
      .contains(tooltipRegex);

    getContainer(`[id="${ratingType}"]:visible`).siblings().first().find('[data-testid="tooltip-close"]').click({ scrollBehavior: false });
  } else {
    getContainer(`[id="${ratingType}"]`).first().scrollIntoView().should('be.visible').trigger('mouseover');
  }
});

Cypress.Commands.add('scrollMonthIntoView', (date) => {
  cy.get(CALENDAR_MONTH).then(($el) => {
    const leftMostDate = new Date($el.attr('data-date'));
    const diff = differenceInCalendarMonths(date, leftMostDate);
    if (diff > 0) {
      clickTimes(cy.get(GO_FORWARD_1_MONTH), diff);
    }
    if (diff < 0) {
      clickTimes(cy.get(GO_BACK_1_MONTH), -diff);
    }
  });
});

Cypress.Commands.add('clickCalendarDay', (date) => {
  const month = format(date, 'MMM yyyy');
  const day = format(date, 'd');
  cy.scrollMonthIntoView(date);
  cy.get(DATE_RANGE_PICKER).contains(month).parent().contains('button', day).click();
});

Cypress.Commands.add('selectDatesWithMouse', (panel = 'first') => {
  const datesInput = panel === 'first' ? cy.get(DATES_INPUT).first() : cy.get(DATES_INPUT).last();
  datesInput.click();
  cy.clickCalendarDay(checkInDate);
  cy.clickCalendarDay(checkOutDate);
  cy.get(DONE_BUTTON).click();
});

Cypress.Commands.add('selectPropertyDatesWithMouse', (checkIn, checkOut) => {
  cy.scrollIntoViewAndClick(DATES_INPUT);
  cy.clickCalendarDay(checkIn);
  cy.clickCalendarDay(checkOut);
  cy.scrollIntoViewAndClick(DONE_BUTTON);
});
