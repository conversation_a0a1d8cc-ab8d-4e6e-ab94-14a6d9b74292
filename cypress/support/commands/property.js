import { HOTELS_PATH } from 'config/env';
import { getDefaults } from '../../../src/lib/enums/search';
import { parse as parseQueryString } from 'query-string';
import { testId } from '../../support/helpers';
import { PAY_WITH_LABEL, USE_POINTS, QUERY_DATE_FORMAT } from '../../support/commonParams';
import { format } from 'date-fns';

const DATES_INPUT = testId('select-date-input');
const DATE_INPUT_FORMAT = 'EEE d MMM, yyyy';

const SELECT_OFFER_BUTTON = testId('offer-checkout-link');
const PERSONAL_DETAILS_FORM = testId('personal-details-form');
const SLIDER_CLASS_NAME = '.rc-slider';
const POINTS_AMOUNT_WRAPPER = testId('points-input');
const CASH_AMOUNT_WRAPPER = testId('cash-input');

const { TEST_PROPERTY_ID } = Cypress.env();

// eslint-disable-next-line no-unused-vars
const { location, sortBy, ...defaultAttributes } = getDefaults();
const ATTRIBUTES = {
  ...defaultAttributes,
  payWith: 'cash',
};

Cypress.Commands.add('setupWaitPropertyPage', () => {
  cy.intercept(`${HOTELS_PATH}/api/ui/properties/*/availability*`).as('property-availability-header');
});

Cypress.Commands.add('waitPropertyPage', () => {
  cy.wait('@property-availability-header');
});

Cypress.Commands.add('visitPropertyWithoutStayAttributes', (propertyId = TEST_PROPERTY_ID) => {
  cy.visit({ url: `${HOTELS_PATH}/properties/${propertyId}`, retryOnStatusCodeFailure: true, retryOnNetworkFailure: true });
});

Cypress.Commands.add('visitPropertyWithStayAttributes', () => {
  const querystring = Object.keys(ATTRIBUTES)
    .map((key) => `${key}=${ATTRIBUTES[key]}`)
    .join('&');

  cy.visit({
    url: `${HOTELS_PATH}/properties/${TEST_PROPERTY_ID}?${querystring}`,
    retryOnStatusCodeFailure: true,
    retryOnNetworkFailure: true,
  });
});

Cypress.Commands.add('visitClassicPropertyWithStayAttributes', () => {
  const querystring = Object.keys(ATTRIBUTES)
    .map((key) => `${key}=${ATTRIBUTES[key]}`)
    .join('&');

  cy.visit({
    url: `${HOTELS_PATH}/properties/${TEST_PROPERTY_ID}?${querystring}`,
    retryOnStatusCodeFailure: true,
    retryOnNetworkFailure: true,
  });
});

Cypress.Commands.add('getBackLinkData', (selector) => {
  cy.get(selector, {
    timeout: 40000,
  })
    .first()
    .then((element) => {
      const backToSearchLocation = new URL(element[0].href);
      const backLinkQueryString = parseQueryString(backToSearchLocation.search);
      return {
        backToSearchLocation,
        backLinkQueryString,
      };
    });
});

Cypress.Commands.add('checkStayAttributes', (queryString, attributes, propertyQueryString = null) => {
  attributes.forEach((attr) => {
    expect(queryString).to.have.property(attr);
    if (propertyQueryString) {
      expect(queryString[attr]).to.eq(propertyQueryString[attr]);
    }
  });
});

Cypress.Commands.add('goToCheckoutPage', (isPointsMode = false) => {
  cy.setupWaitPropertyPage();
  cy.visitPropertyWithStayAttributes();

  if (isPointsMode) {
    cy.scrollIntoViewAndClick(PAY_WITH_LABEL, USE_POINTS);
  }

  cy.waitPropertyPage();

  cy.location().its('search').as('propertyQueryString');

  cy.get(SELECT_OFFER_BUTTON).first().click();

  cy.get(PERSONAL_DETAILS_FORM, { timeout: 30000 }).should('be.visible');

  cy.location().as('checkoutLoc').its('search').as('checkoutQueryString');
});

Cypress.Commands.add('assertSelectedDates', (checkIn, checkOut) => {
  cy.assertRoutedToQueryParam('checkIn', format(checkIn, QUERY_DATE_FORMAT));
  cy.assertRoutedToQueryParam('checkOut', format(checkOut, QUERY_DATE_FORMAT));
  cy.get(DATES_INPUT).should('have.value', `${format(checkIn, DATE_INPUT_FORMAT)} - ${format(checkOut, DATE_INPUT_FORMAT)}`);
});

Cypress.Commands.add('updateOccupantCount', (occupantType, change, initialCount, expectedCount) => {
  const occupant = `[data-testid="occupant-picker-${occupantType}"]`;
  const incrementButton = `${occupant} [data-testid="increment"]`;
  const decrementButton = `${occupant} [data-testid="decrement"]`;
  const countDisplay = `${occupant} [data-testid="occupant-count"]`;

  cy.get(countDisplay).should('have.text', initialCount.toString());

  if (change > 0) {
    Cypress._.times(change, () => cy.get(incrementButton).click());
  } else if (change < 0) {
    Cypress._.times(Math.abs(change), () => cy.get(decrementButton).click());
  }

  cy.get(countDisplay).should('have.text', expectedCount.toString());
});

Cypress.Commands.add('getInitialCashAmountFromSelectButton', (selector) => {
  cy.get(selector)
    .first()
    .then((element) => {
      const checkoutLoc = new URL(element[0].href);
      const queryParams = parseQueryString(checkoutLoc.search);
      const initialCashParam = queryParams['initialCash'];

      const formattedCash = parseFloat(initialCashParam.replace(/,/g, '')).toFixed(2).toLocaleString('en-US');

      return formattedCash;
    });
});

Cypress.Commands.add('testPointsSliderMovement', (direction, cashComparison, pointsComparison) => {
  let initialCashAmountInSlider;
  let initialPointsAmountInSlider;

  cy.get(CASH_AMOUNT_WRAPPER)
    .invoke('val')
    .then((val) => (initialCashAmountInSlider = val));
  cy.get(POINTS_AMOUNT_WRAPPER)
    .invoke('val')
    .then((val) => (initialPointsAmountInSlider = val));

  cy.get(SLIDER_CLASS_NAME).first().click(direction);
  cy.wait(2000);

  cy.get(CASH_AMOUNT_WRAPPER)
    .invoke('val')
    .then((finalCashAmountInSlider) => {
      const initialCash = parseFloat(initialCashAmountInSlider.replace(/,/g, ''));
      const finalCash = parseFloat(finalCashAmountInSlider.replace(/,/g, ''));

      if (cashComparison === 'increase') {
        expect(finalCash).to.be.greaterThan(initialCash);
      } else {
        expect(finalCash).to.be.lessThan(initialCash);
      }

      cy.getInitialCashAmountFromSelectButton(SELECT_OFFER_BUTTON).then((initialCashAmountInSelectButton) => {
        expect(initialCashAmountInSelectButton).to.equal(finalCashAmountInSlider);
      });
    });

  cy.get(POINTS_AMOUNT_WRAPPER)
    .invoke('val')
    .then((finalPointsAmountInSlider) => {
      const initialPoints = parseFloat(initialPointsAmountInSlider);
      const finalPoints = parseFloat(finalPointsAmountInSlider);

      if (pointsComparison === 'increase') {
        expect(finalPoints).to.be.greaterThan(initialPoints);
      } else {
        expect(finalPoints).to.be.lessThan(initialPoints);
      }
    });
});
