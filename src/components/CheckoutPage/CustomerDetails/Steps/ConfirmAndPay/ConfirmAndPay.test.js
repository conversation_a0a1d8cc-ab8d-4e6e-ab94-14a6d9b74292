import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { default as configureStore } from 'redux-mock-store';
import { ThemeProvider } from 'emotion-theming';
import merge from 'lodash/merge';
import { Decimal } from 'decimal.js';
import { useRouter } from 'next/router';
import ConfirmAndPay from './ConfirmAndPay';
import { INITIAL, EDITING, COMPLETE } from 'components/StepWizard';
import { useDataLayer } from 'hooks/useDataLayer';
import {
  getRequiresCreditCardPayment,
  getIsCreditCardValid,
  getPayableNowCashAmount,
  getIsPaymentMethodChanged,
} from 'store/checkout/checkoutSelectors';
import { getStayDates, getQuote } from 'store/quote/quoteSelectors';
import { createBooking } from 'store/booking/bookingActions';
import { getIsCreating } from 'store/booking/bookingSelectors';
import useAdyenDropIn from './hooks/useAdyenDropIn';
import usePaymentMethods from 'components/AdyenDropIn/hooks/usePaymentMethods';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import theme from 'lib/theme';

const paymentMethods = [
  {
    type: 'scheme',
    name: 'Credit Card',
    brands: ['visa', 'mc', 'amex'],
  },
];

jest.mock('hooks/useDataLayer');
jest.mock('store/quote/quoteSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/booking/bookingSelectors');
jest.mock('store/checkout/checkoutSelectors');
jest.mock('store/ui/uiSelectors');
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('components/AdyenDropIn/hooks/usePaymentMethods');
jest.mock('./hooks/useAdyenDropIn');
jest.mock('hooks/optimizely/useAvailableRoomsMessage');
jest.mock('./PaymentMethodChanged', () => () => <div data-testid="payment-method-changed">PaymentMethodChanged</div>);
jest.mock('components/AdyenDropIn/components/AdyenDropInLoaderSkeleton', () => () => (
  <div data-testid="payment-loader-skeleton">AdyenDropInLoaderSkeleton</div>
));

jest.mock('components/StepWizard', () => ({
  StepHeader: ({ title, _edit, _hasState, subTitle }) => (
    <div data-testid="step-header" data-title={title}>
      StepHeader
      {subTitle}
    </div>
  ),
  INITIAL: 'INITIAL',
  EDITING: 'EDITING',
  COMPLETE: 'COMPLETE',
}));

jest.mock('./BookingSummary', () => () => <div data-testid="booking-summary">BookingSummary</div>);
jest.mock('./BookingProgressDialog', () => () => <div data-testid="booking-progress-dialog">BookingProgressDialog</div>);
jest.mock('./PriceDetails', () => ({ checkIn, checkOut, _property, _offer }) => (
  <div data-testid="price-details" data-checkin={checkIn} data-checkout={checkOut}>
    PriceDetails
  </div>
));
jest.mock('./CreditCard', () => () => <div data-testid="credit-card">CreditCard</div>);
jest.mock('./CreditCardIcons', () => () => <div>CreditCardIcons</div>);

const mockStore = configureStore([]);
let scrollMock;
let mockFormIsValid = true;
const mockValidate = jest.fn();
const mockUpdatePayments = jest.fn();
jest.mock('components/AdyenDropIn', () => {
  const React = require('react');
  const { forwardRef, useImperativeHandle } = React;

  return {
    __esModule: true,
    default: forwardRef((_, ref) => {
      useImperativeHandle(ref, () => ({
        isValid: () => mockFormIsValid,
        validate: mockValidate,
        updatePayments: mockUpdatePayments,
      }));

      return <div ref={ref}>AdyenDropIn</div>;
    }),
  };
});

const defaultAction = {
  showMessage: false,
  max_rooms_cutoff: 5,
  isPriceStrikethrough: false,
};

const mockHasState =
  (expectedState) =>
  (...states) =>
    states.includes(expectedState);

const baseProps = {
  step: {
    updateFormData: jest.fn(),
    edit: jest.fn(),
    hasState: mockHasState(INITIAL),
    completeStep: jest.fn(),
  },
};
const emitInteractionEvent = jest.fn();

const renderComponent = (props = {}, storeState = {}) => {
  const store = mockStore(storeState);
  jest.spyOn(store, 'dispatch');
  const finalProps = merge({ ...baseProps }, props);

  return {
    ...render(
      <Provider store={store}>
        <ThemeProvider theme={theme}>
          <ConfirmAndPay {...finalProps} />
        </ThemeProvider>
      </Provider>,
    ),
    store,
  };
};

const payableNowCashAmount = new Decimal(532.24);
const checkIn = new Date(2020, 9, 1);
const checkOut = new Date(2020, 9, 2);
const stayDates = { checkIn, checkOut };
const property = {};
const standardOffer = { type: 'standard', cancellationPolicy: { description: 'offer description' } };
const quoteWithStandardOffer = { property, offer: standardOffer };
const mockRouter = {
  query: {},
};

beforeEach(() => {
  if (!HTMLElement.prototype.scrollIntoView) {
    HTMLElement.prototype.scrollIntoView = () => {};
  }
  scrollMock = jest.spyOn(HTMLElement.prototype, 'scrollIntoView').mockImplementation(() => {});

  getPayableNowCashAmount.mockReturnValue(payableNowCashAmount);
  getRequiresCreditCardPayment.mockReturnValue(true);
  getStayDates.mockReturnValue(stayDates);
  getQuote.mockReturnValue(quoteWithStandardOffer);
  useDataLayer.mockReturnValue({ emitInteractionEvent });

  useRouter.mockReturnValue(mockRouter);
  useAdyenDropIn.mockReturnValue({
    isReady: true,
    isAdyenDropIn: false,
  });
  usePaymentMethods.mockReturnValue({
    fetch: () => {},
    isPaymentMethodsReady: true,
    paymentMethods,
    storedPaymentMethods: [],
  });
  useAvailableRoomsMessage.mockReturnValue({
    isReady: true,
    showMessage: false,
    max_rooms_cutoff: 5,
  });
});

afterEach(() => {
  getIsCreating.mockReturnValue(false);
  emitInteractionEvent.mockClear();
  jest.resetAllMocks();
  scrollMock.mockRestore();
});

[INITIAL, COMPLETE].forEach((state) => {
  describe(`with a state of ${state}`, () => {
    beforeEach(() => {
      getRequiresCreditCardPayment.mockReturnValue(false);
    });

    it('renders the StepHeader', () => {
      renderComponent({ step: { hasState: mockHasState(state) } });
      const stepHeader = screen.getByTestId('step-header');
      expect(stepHeader).toBeInTheDocument();
      expect(stepHeader).toHaveAttribute('data-title', 'Confirm & pay');
    });

    it('does not render the review summary information', () => {
      renderComponent({ step: { hasState: mockHasState(state) } });
      expect(screen.queryByTestId('review-summary')).not.toBeInTheDocument();
    });

    describe('when a credit card payment is required', () => {
      beforeEach(() => {
        getRequiresCreditCardPayment.mockReturnValue(true);
      });

      it('renders the StepHeader with CreditCardIcons when feature flag is off', () => {
        renderComponent({ step: { hasState: mockHasState(state) } });
        const stepHeader = screen.getByTestId('step-header');
        expect(stepHeader).toBeInTheDocument();
        expect(stepHeader).toHaveAttribute('data-title', 'Confirm & pay');
        expect(screen.getByText('CreditCardIcons')).toBeInTheDocument();
      });
    });
  });
});

describe('with a state of EDITING', () => {
  it('renders the BookingSummary', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByTestId('booking-summary')).toBeInTheDocument();
  });

  it('renders the PriceDetails', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    const priceDetails = screen.getByTestId('price-details');
    expect(priceDetails).toBeInTheDocument();
    expect(priceDetails).toHaveAttribute('data-checkin', checkIn.toString());
    expect(priceDetails).toHaveAttribute('data-checkout', checkOut.toString());
  });

  it('renders the payment button disabled', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByTestId('payment-button')).toBeDisabled();
  });

  describe('when a credit card payment is required and feature flag is off', () => {
    beforeEach(() => {
      getRequiresCreditCardPayment.mockReturnValue(true);
    });

    it('renders the CreditCard form', () => {
      renderComponent({ step: { hasState: mockHasState(EDITING) } });
      expect(screen.getByTestId('credit-card')).toBeInTheDocument();
    });
  });

  describe('when clicking the hotel cancellation policy textButton', () => {
    it('opens the modal with the cancellation policy', async () => {
      renderComponent({ step: { hasState: mockHasState(EDITING) } });
      await userEvent.click(screen.getByTestId('cancellation-policy-button'));
      const cancellationPolicyDescription = screen.getByTestId('cancellation-policy-description');
      expect(cancellationPolicyDescription).toHaveTextContent(
        'Cancellation policy' + quoteWithStandardOffer.offer.cancellationPolicy.description,
      );
    });

    it('emits a user interactive event', async () => {
      renderComponent({ step: { hasState: mockHasState(EDITING) } });
      await userEvent.click(screen.getByTestId('cancellation-policy-button'));
      expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Policy and Terms Links', value: 'Hotel Cancellation Policy Selected' });
    });
  });

  describe('when the terms and conditions are selected', () => {
    it('dispatches an event to the data layer', async () => {
      renderComponent({ step: { hasState: mockHasState(EDITING) } });

      await userEvent.click(screen.getByTestId('accept-terms'));

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Policy and Terms Links',
        value: 'Agree Opted In',
      });
    });

    describe('when the credit card is valid', () => {
      beforeEach(() => {
        getIsCreditCardValid.mockReturnValue(true);
      });

      it('enables the payment button', async () => {
        renderComponent({ step: { hasState: mockHasState(EDITING) } });

        await userEvent.click(screen.getByTestId('accept-terms'));

        expect(screen.getByTestId('payment-button')).not.toBeDisabled();
      });

      describe('when creating', () => {
        it('disables the payment button', async () => {
          getIsCreating.mockReturnValue(true);
          renderComponent({ step: { hasState: mockHasState(EDITING) } });

          await userEvent.click(screen.getByTestId('accept-terms'));

          expect(screen.getByTestId('payment-button')).toBeDisabled();
        });
      });

      describe('clicking the payment button', () => {
        it('dispatches the createBooking action and event to the data layer when feature flag is off', async () => {
          const { store } = renderComponent({ step: { hasState: mockHasState(EDITING) } });

          await userEvent.click(screen.getByTestId('accept-terms'));
          await userEvent.click(screen.getByTestId('payment-button'));

          expect(store.dispatch).toHaveBeenCalledWith(createBooking(defaultAction));
          expect(emitInteractionEvent).toHaveBeenCalledWith({
            type: 'Pay Now Button',
            value: 'Button Selected',
          });
        });
      });
    });

    describe('when the credit card is not valid', () => {
      beforeEach(() => {
        getIsCreditCardValid.mockReturnValue(false);
      });

      it('keeps the payment button disabled even when terms are accepted', async () => {
        renderComponent({ step: { hasState: mockHasState(EDITING) } });

        await userEvent.click(screen.getByTestId('accept-terms'));

        expect(screen.getByTestId('payment-button')).toBeDisabled();
      });

      it('does not allow booking submission when payment button is clicked', async () => {
        const { store } = renderComponent({ step: { hasState: mockHasState(EDITING) } });

        await userEvent.click(screen.getByTestId('accept-terms'));

        expect(screen.getByTestId('payment-button')).toBeDisabled();
        expect(store.dispatch).not.toHaveBeenCalledWith(createBooking(defaultAction));
      });
    });
  });

  it('renders the BookingProgressDialog while the booking is being created', () => {
    getIsCreating.mockReturnValue(true);
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByTestId('booking-progress-dialog')).toBeInTheDocument();
  });

  it('does not render the BookingProgressDialog when the booking is not being created', () => {
    getIsCreating.mockReturnValue(false);
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.queryByTestId('booking-progress-dialog')).not.toBeInTheDocument();
  });
});

describe('renders adyen drop-in when credit card payment is required and feature flag is on', () => {
  beforeEach(() => {
    getRequiresCreditCardPayment.mockReturnValue(true);
    useAdyenDropIn.mockReturnValue({
      isReady: true,
      isAdyenDropIn: true,
    });
    mockFormIsValid = true;
    mockValidate.mockReset();
    mockUpdatePayments.mockReset();
  });

  it('renders the AdyenDropIn form', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByText('AdyenDropIn')).toBeInTheDocument();
  });

  it('dispatches the createBooking action and event to the data layer when form is valid', async () => {
    mockFormIsValid = true;
    const { store } = renderComponent({ step: { hasState: mockHasState(EDITING) } });

    await userEvent.click(screen.getByTestId('accept-terms'));
    await userEvent.click(screen.getByTestId('payment-button'));

    expect(mockUpdatePayments).toHaveBeenCalled();
    expect(store.dispatch).toHaveBeenCalledWith(createBooking(defaultAction));
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Pay Now Button',
      value: 'Button Selected',
    });
  });

  it('validate form when form is invalid', async () => {
    mockFormIsValid = false;
    renderComponent({ step: { hasState: mockHasState(EDITING) } });

    await userEvent.click(screen.getByTestId('accept-terms'));
    await userEvent.click(screen.getByTestId('payment-button'));

    expect(mockValidate).toHaveBeenCalled();
  });

  it('does not submit booking and displays error when terms and conditions are not accepted', async () => {
    mockFormIsValid = true;
    const { store } = renderComponent({ step: { hasState: mockHasState(EDITING) } });

    await userEvent.click(screen.getByTestId('payment-button'));

    expect(scrollMock).toHaveBeenCalledWith({ behavior: 'smooth' });
    expect(screen.getByText('Accept terms and conditions to continue')).toBeInTheDocument();
    expect(
      screen.getByText('Please confirm you have read and accepted the terms and conditions to proceed with your booking.'),
    ).toBeInTheDocument();
    expect(mockUpdatePayments).not.toHaveBeenCalled();
    expect(store.dispatch).not.toHaveBeenCalledWith(
      expect.objectContaining({
        type: 'booking/CREATE_BOOKING',
      }),
    );
    expect(emitInteractionEvent).not.toHaveBeenCalled();
  });

  it('renders the StepHeader without CreditCardIcons when feature flag is on', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    const stepHeader = screen.getByTestId('step-header');
    expect(stepHeader).toBeInTheDocument();
    expect(stepHeader).toHaveAttribute('data-title', 'Confirm & pay');
    expect(screen.queryByText('CreditCardIcons')).not.toBeInTheDocument();
  });

  it('does not render the CreditCard form when feature flag is on', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.queryByTestId('credit-card')).not.toBeInTheDocument();
  });

  it('renders the payment method heading when qff user is logged in and storedPaymentMethods data is available', () => {
    usePaymentMethods.mockReturnValue({
      fetch: () => {},
      isPaymentMethodsReady: true,
      qepgVault: {
        memberId: '**********',
        accountType: 'QFF',
      },
      paymentMethods,
      storedPaymentMethods: [
        {
          id: '841599548723410C',
          supportedShopperInteractions: ['Ecommerce'],
          type: 'scheme',
          name: 'VISA',
          brand: 'visa',
          lastFour: '1111',
          expiryMonth: '10',
          expiryYear: '2030',
        },
      ],
    });

    renderComponent({ step: { hasState: mockHasState(EDITING) } });

    expect(screen.getByRole('heading', { level: 3, name: 'Payment method' })).toBeInTheDocument();
  });

  it('renders loading skeleton when adyen drop-in is not ready', () => {
    useAdyenDropIn.mockReturnValue({
      isReady: false,
      isAdyenDropIn: true,
    });
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByTestId('payment-loader-skeleton')).toBeInTheDocument();
    expect(screen.queryByTestId('adyen-drop-in')).not.toBeInTheDocument();
  });

  it('disables the payment button when adyen drop-in is not ready', async () => {
    useAdyenDropIn.mockReturnValue({
      isReady: false,
      isAdyenDropIn: true,
    });
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    await userEvent.click(screen.getByTestId('accept-terms'));
    expect(screen.getByTestId('payment-button')).toBeDisabled();
  });
});

describe('when a credit card payment is not required', () => {
  beforeEach(() => {
    getRequiresCreditCardPayment.mockReturnValue(false);
  });

  it('does not render the credit card or Adyen Drop-in sections', () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.queryByTestId('credit-card')).not.toBeInTheDocument();
    expect(screen.queryByText('AdyenDropIn')).not.toBeInTheDocument();
  });

  it('enables the payment button when terms are accepted, even without a credit card', async () => {
    getIsCreditCardValid.mockReturnValue(true);
    renderComponent({ step: { hasState: mockHasState(EDITING) } });

    await userEvent.click(screen.getByTestId('accept-terms'));
    expect(screen.getByTestId('payment-button')).not.toBeDisabled();
  });

  it('dispatches the createBooking action when the payment button is clicked and terms are accepted', async () => {
    getIsCreditCardValid.mockReturnValue(true); // assuming a points booking
    const { store } = renderComponent({ step: { hasState: mockHasState(EDITING) } });
    await userEvent.click(screen.getByTestId('accept-terms'));
    await userEvent.click(screen.getByTestId('payment-button'));

    expect(store.dispatch).toHaveBeenCalledWith(createBooking(defaultAction));
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Pay Now Button',
      value: 'Button Selected',
    });
  });
});

describe('when clicking the Privacy Policy link', () => {
  it('emits a user interactive event', async () => {
    renderComponent({ step: { hasState: mockHasState(EDITING) } });

    await userEvent.click(screen.getByRole('link', { name: 'View privacy policy in a new tab' }));

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Policy and Terms Links',
      value: 'Privacy Policy Selected',
    });
  });
});

describe('when the payment method has changed', () => {
  it('renders the PaymentMethodChanged component', () => {
    getIsPaymentMethodChanged.mockReturnValue(true);
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.getByTestId('payment-method-changed')).toBeInTheDocument();
  });

  it('does not render the PaymentMethodChanged component when not changed', () => {
    getIsPaymentMethodChanged.mockReturnValue(false);
    renderComponent({ step: { hasState: mockHasState(EDITING) } });
    expect(screen.queryByTestId('payment-method-changed')).not.toBeInTheDocument();
  });
});

describe('renders loading skeleton when adyen drop-in is not ready', () => {
  it('renders the loading skeleton with qepgVault and storedPaymentMethods props', () => {
    useAdyenDropIn.mockReturnValue({
      isReady: false,
      isAdyenDropIn: true,
    });
    usePaymentMethods.mockReturnValue({
      fetch: () => {},
      isPaymentMethodsReady: false,
      qepgVault: { memberId: '123' },
      storedPaymentMethods: [{ id: 'card1' }],
    });
    renderComponent({ step: { hasState: mockHasState(EDITING) } });

    const skeleton = screen.getByTestId('payment-loader-skeleton');
    expect(skeleton).toBeInTheDocument();
  });
});
